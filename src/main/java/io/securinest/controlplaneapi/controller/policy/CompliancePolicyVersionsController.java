package io.securinest.controlplaneapi.controller.policy;

import io.securinest.controlplaneapi.dto.policy.CompliancePolicyVersionResponse;
import io.securinest.controlplaneapi.dto.policy.CreatePolicyVersionRequest;
import io.securinest.controlplaneapi.dto.policy.UpdatePolicyVersionStatusRequest;
import io.securinest.controlplaneapi.entity.shared.PolicyState;
import io.securinest.controlplaneapi.service.policy.CompliancePolicyVersionService;
import io.securinest.controlplaneapi.util.shared.ResponseEntityUtils;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
@RequiredArgsConstructor
@RequestMapping("/tenants/{tenantId}/policies/{policyId}/versions")
public class CompliancePolicyVersionsController {

    private final CompliancePolicyVersionService versionService;

    @PostMapping
    public ResponseEntity<CompliancePolicyVersionResponse> createVersion(
            @PathVariable UUID tenantId,
            @PathVariable UUID policyId,
            @Valid @RequestBody CreatePolicyVersionRequest request,
            @RequestHeader(name = "X-Debug-UserId", required = false) UUID debugUserId,
            @RequestHeader(name = "X-Request-Id", required = false) String requestId) {

        if (debugUserId == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        CompliancePolicyVersionResponse version = versionService.createVersion(tenantId, policyId, request, debugUserId, requestId);
        return ResponseEntityUtils.created(version);
    }

    @GetMapping
    public ResponseEntity<Page<CompliancePolicyVersionResponse>> listVersions(
            @PathVariable UUID tenantId,
            @PathVariable UUID policyId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestHeader(name = "X-Debug-UserId", required = false) UUID debugUserId) {

        if (debugUserId == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        Pageable pageable = PageRequest.of(page, size);
        Page<CompliancePolicyVersionResponse> versions = versionService.listVersions(tenantId, policyId, pageable);

        return ResponseEntityUtils.ok(versions);
    }

    @GetMapping("/{versionNo}")
    public ResponseEntity<CompliancePolicyVersionResponse> getVersion(
            @PathVariable UUID tenantId,
            @PathVariable UUID policyId,
            @PathVariable int versionNo,
            @RequestHeader(name = "X-Debug-UserId", required = false) UUID debugUserId) {

        if (debugUserId == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        CompliancePolicyVersionResponse version = versionService.getVersion(tenantId, policyId, versionNo, debugUserId);
        return ResponseEntityUtils.ok(version);
    }

    @PatchMapping("/{versionNo}")
    public ResponseEntity<CompliancePolicyVersionResponse> updateVersionStatus(
            @PathVariable UUID tenantId,
            @PathVariable UUID policyId,
            @PathVariable int versionNo,
            @Valid @RequestBody UpdatePolicyVersionStatusRequest request,
            @RequestHeader(name = "X-Debug-UserId", required = false) UUID debugUserId,
            @RequestHeader(name = "X-Request-Id", required = false) String requestId) {

        if (debugUserId == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        PolicyState newStatus;
        try {
            newStatus = PolicyState.valueOf(request.status().toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid status: " + request.status());
        }

        CompliancePolicyVersionResponse version = versionService.updateStatus(
                tenantId, policyId, versionNo, newStatus, debugUserId, requestId);

        return ResponseEntityUtils.ok(version);
    }
}
