package io.securinest.controlplaneapi.mapper.policy;

import io.securinest.controlplaneapi.dto.policy.CompliancePolicyVersionResponse;
import io.securinest.controlplaneapi.dto.policy.CreatePolicyVersionRequest;
import io.securinest.controlplaneapi.entity.policy.CompliancePolicyVersion;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(
        componentModel = "spring",
        injectionStrategy = InjectionStrategy.CONSTRUCTOR,
        unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface CompliancePolicyVersionMapper {

    @Mapping(target = "status", expression = "java(entity.getStatus().name())")
    CompliancePolicyVersionResponse toDto(CompliancePolicyVersion entity);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "policyId", ignore = true)
    @Mapping(target = "versionNo", ignore = true)
    @Mapping(target = "authorId", ignore = true)
    @Mapping(target = "status", expression = "java(io.securinest.controlplaneapi.entity.shared.PolicyState.DRAFT)")
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "version", ignore = true)
    CompliancePolicyVersion fromCreateRequest(CreatePolicyVersionRequest request);

}
